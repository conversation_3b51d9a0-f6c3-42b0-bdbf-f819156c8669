apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: ai-react-frontend-project
  namespace: argocd
  labels:
    app.kubernetes.io/name: ai-react-frontend-project
    environment: dev
spec:
  description: "AI React Frontend Project for GitOps deployment"
  sourceRepos:
  - 'https://github.com/ChidhagniConsulting/gitops-argocd-apps.git'
  - 'https://github.com/ChidhagniConsulting/gitops-argocd-apps'
  destinations:
  - namespace: ai-react-frontend-dev
    server: https://kubernetes.default.svc
  - namespace: argocd
    server: https://kubernetes.default.svc
  clusterResourceWhitelist:
  - group: ''
    kind: Namespace
  - group: ''
    kind: PersistentVolume
  - group: storage.k8s.io
    kind: StorageClass
  namespaceResourceWhitelist:
  - group: ''
    kind: ConfigMap
  - group: ''
    kind: Secret
  - group: ''
    kind: Service
  - group: ''
    kind: PersistentVolumeClaim
  - group: apps
    kind: Deployment
  - group: batch
    kind: Job
  - group: networking.k8s.io
    kind: Ingress
  roles:
  - name: admin
    description: "Admin access to ai-react-frontend project"
    policies:
    - p, proj:ai-react-frontend-project:admin, applications, *, ai-react-frontend-project/*, allow
    - p, proj:ai-react-frontend-project:admin, repositories, *, *, allow
    groups:
    - argocd:admin
