apiVersion: v1
kind: Service
metadata:
  name: ai-react-frontend
  namespace: ai-react-frontend-dev
  labels:
    app: ai-react-frontend
    component: service
    environment: dev
    app-type: react-frontend
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    service.kubernetes.io/managed-by: "gitops-argocd"
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 80
    protocol: TCP
    name: http
  selector:
    app: ai-react-frontend
    app.kubernetes.io/name: ai-react-frontend
  sessionAffinity: None
  # Service-specific configurations for different environments
